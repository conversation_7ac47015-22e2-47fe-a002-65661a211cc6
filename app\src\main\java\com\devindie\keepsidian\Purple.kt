package com.yangdai.opennote.presentation.theme

import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.ui.graphics.Color

private val primaryLight = Color(0xFF7F4D7A)
private val onPrimaryLight = Color(0xFFFFFFFF)
private val primaryContainerLight = Color(0xFFFFD7F6)
private val onPrimaryContainerLight = Color(0xFF330833)
private val secondaryLight = Color(0xFF7F4D7B)
private val onSecondaryLight = Color(0xFFFFFFFF)
private val secondaryContainerLight = Color(0xFFFFD7F7)
private val onSecondaryContainerLight = Color(0xFF330833)
private val tertiaryLight = Color(0xFF8F4C37)
private val onTertiaryLight = Color(0xFFFFFFFF)
private val tertiaryContainerLight = Color(0xFFFFDBD1)
private val onTertiaryContainerLight = Color(0xFF3A0B00)
private val errorLight = Color(0xFF904A43)
private val onErrorLight = Color(0xFFFFFFFF)
private val errorContainerLight = Color(0xFFFFDAD5)
private val onErrorContainerLight = Color(0xFF3B0907)
private val backgroundLight = Color(0xFFFFF7F9)
private val onBackgroundLight = Color(0xFF201A1E)
private val surfaceLight = Color(0xFFFFF7F9)
private val onSurfaceLight = Color(0xFF201A1E)
private val surfaceVariantLight = Color(0xFFEEDEE7)
private val onSurfaceVariantLight = Color(0xFF4E444B)
private val outlineLight = Color(0xFF80747C)
private val outlineVariantLight = Color(0xFFD1C2CB)
private val scrimLight = Color(0xFF000000)
private val inverseSurfaceLight = Color(0xFF352E33)
private val inverseOnSurfaceLight = Color(0xFFFAEDF4)
private val inversePrimaryLight = Color(0xFFF1B3E7)
private val surfaceDimLight = Color(0xFFE3D7DD)
private val surfaceBrightLight = Color(0xFFFFF7F9)
private val surfaceContainerLowestLight = Color(0xFFFFFFFF)
private val surfaceContainerLowLight = Color(0xFFFDF0F7)
private val surfaceContainerLight = Color(0xFFF7EBF1)
private val surfaceContainerHighLight = Color(0xFFF1E5EB)
private val surfaceContainerHighestLight = Color(0xFFEBDFE6)

private val primaryDark = Color(0xFFF1B3E7)
private val onPrimaryDark = Color(0xFF4C1F49)
private val primaryContainerDark = Color(0xFF653661)
private val onPrimaryContainerDark = Color(0xFFFFD7F6)
private val secondaryDark = Color(0xFFF0B3E7)
private val onSecondaryDark = Color(0xFF4B1F4A)
private val secondaryContainerDark = Color(0xFF653662)
private val onSecondaryContainerDark = Color(0xFFFFD7F7)
private val tertiaryDark = Color(0xFFFFB59F)
private val onTertiaryDark = Color(0xFF561F0E)
private val tertiaryContainerDark = Color(0xFF723522)
private val onTertiaryContainerDark = Color(0xFFFFDBD1)
private val errorDark = Color(0xFFFFB4AB)
private val onErrorDark = Color(0xFF561E19)
private val errorContainerDark = Color(0xFF73342D)
private val onErrorContainerDark = Color(0xFFFFDAD5)
private val backgroundDark = Color(0xFF171216)
private val onBackgroundDark = Color(0xFFEBDFE6)
private val surfaceDark = Color(0xFF171216)
private val onSurfaceDark = Color(0xFFEBDFE6)
private val surfaceVariantDark = Color(0xFF4E444B)
private val onSurfaceVariantDark = Color(0xFFD1C2CB)
private val outlineDark = Color(0xFF9A8D95)
private val outlineVariantDark = Color(0xFF4E444B)
private val scrimDark = Color(0xFF000000)
private val inverseSurfaceDark = Color(0xFFEBDFE6)
private val inverseOnSurfaceDark = Color(0xFF352E33)
private val inversePrimaryDark = Color(0xFF7F4D7A)
private val surfaceDimDark = Color(0xFF171216)
private val surfaceBrightDark = Color(0xFF3E373C)
private val surfaceContainerLowestDark = Color(0xFF120D11)
private val surfaceContainerLowDark = Color(0xFF201A1E)
private val surfaceContainerDark = Color(0xFF241E22)
private val surfaceContainerHighDark = Color(0xFF2F282D)
private val surfaceContainerHighestDark = Color(0xFF3A3338)


val LightPurpleColors = lightColorScheme(
    primary = primaryLight,
    onPrimary = onPrimaryLight,
    primaryContainer = primaryContainerLight,
    onPrimaryContainer = onPrimaryContainerLight,
    secondary = secondaryLight,
    onSecondary = onSecondaryLight,
    secondaryContainer = secondaryContainerLight,
    onSecondaryContainer = onSecondaryContainerLight,
    tertiary = tertiaryLight,
    onTertiary = onTertiaryLight,
    tertiaryContainer = tertiaryContainerLight,
    onTertiaryContainer = onTertiaryContainerLight,
    error = errorLight,
    onError = onErrorLight,
    errorContainer = errorContainerLight,
    onErrorContainer = onErrorContainerLight,
    background = backgroundLight,
    onBackground = onBackgroundLight,
    surface = surfaceLight,
    onSurface = onSurfaceLight,
    surfaceVariant = surfaceVariantLight,
    onSurfaceVariant = onSurfaceVariantLight,
    outline = outlineLight,
    inverseOnSurface = inverseOnSurfaceLight,
    inverseSurface = inverseSurfaceLight,
    inversePrimary = inversePrimaryLight,
    outlineVariant = outlineVariantLight,
    scrim = scrimLight,
    surfaceContainerLowest = surfaceContainerLowestLight,
    surfaceContainerLow = surfaceContainerLowLight,
    surfaceContainer = surfaceContainerLight,
    surfaceContainerHigh = surfaceContainerHighLight,
    surfaceContainerHighest = surfaceContainerHighestLight,
    surfaceBright = surfaceBrightLight,
    surfaceDim = surfaceDimLight,
)


val DarkPurpleColors = darkColorScheme(
    primary = primaryDark,
    onPrimary = onPrimaryDark,
    primaryContainer = primaryContainerDark,
    onPrimaryContainer = onPrimaryContainerDark,
    secondary = secondaryDark,
    onSecondary = onSecondaryDark,
    secondaryContainer = secondaryContainerDark,
    onSecondaryContainer = onSecondaryContainerDark,
    tertiary = tertiaryDark,
    onTertiary = onTertiaryDark,
    tertiaryContainer = tertiaryContainerDark,
    onTertiaryContainer = onTertiaryContainerDark,
    error = errorDark,
    onError = onErrorDark,
    errorContainer = errorContainerDark,
    onErrorContainer = onErrorContainerDark,
    background = backgroundDark,
    onBackground = onBackgroundDark,
    surface = surfaceDark,
    onSurface = onSurfaceDark,
    surfaceVariant = surfaceVariantDark,
    onSurfaceVariant = onSurfaceVariantDark,
    outline = outlineDark,
    inverseOnSurface = inverseOnSurfaceDark,
    inverseSurface = inverseSurfaceDark,
    inversePrimary = inversePrimaryDark,
    outlineVariant = outlineVariantDark,
    scrim = scrimDark,
    surfaceContainerLowest = surfaceContainerLowestDark,
    surfaceContainerLow = surfaceContainerLowDark,
    surfaceContainer = surfaceContainerDark,
    surfaceContainerHigh = surfaceContainerHighDark,
    surfaceContainerHighest = surfaceContainerHighestDark,
    surfaceBright = surfaceBrightDark,
    surfaceDim = surfaceDimDark,
)