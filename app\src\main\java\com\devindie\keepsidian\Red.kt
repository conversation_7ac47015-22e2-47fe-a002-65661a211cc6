package com.yangdai.opennote.presentation.theme

import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.ui.graphics.Color

private val primaryLight = Color(0xFF904A46)
private val onPrimaryLight = Color(0xFFFFFFFF)
private val primaryContainerLight = Color(0xFFFFDAD7)
private val onPrimaryContainerLight = Color(0xFF3B0909)
private val secondaryLight = Color(0xFF775654)
private val onSecondaryLight = Color(0xFFFFFFFF)
private val secondaryContainerLight = Color(0xFFFFDAD7)
private val onSecondaryContainerLight = Color(0xFF2C1513)
private val tertiaryLight = Color(0xFF725B2E)
private val onTertiaryLight = Color(0xFFFFFFFF)
private val tertiaryContainerLight = Color(0xFFFFDEA6)
private val onTertiaryContainerLight = Color(0xFF271900)
private val errorLight = Color(0xFFBA1A1A)
private val onErrorLight = Color(0xFFFFFFFF)
private val errorContainerLight = Color(0xFFFFDAD6)
private val onErrorContainerLight = Color(0xFF410002)
private val backgroundLight = Color(0xFFFFF8F7)
private val onBackgroundLight = Color(0xFF231919)
private val surfaceLight = Color(0xFFFFF8F7)
private val onSurfaceLight = Color(0xFF231919)
private val surfaceVariantLight = Color(0xFFF5DDDB)
private val onSurfaceVariantLight = Color(0xFF534342)
private val outlineLight = Color(0xFF857371)
private val outlineVariantLight = Color(0xFFD8C2BF)
private val scrimLight = Color(0xFF000000)
private val inverseSurfaceLight = Color(0xFF382E2D)
private val inverseOnSurfaceLight = Color(0xFFFFEDEB)
private val inversePrimaryLight = Color(0xFFFFB3AE)
private val surfaceDimLight = Color(0xFFE8D6D4)
private val surfaceBrightLight = Color(0xFFFFF8F7)
private val surfaceContainerLowestLight = Color(0xFFFFFFFF)
private val surfaceContainerLowLight = Color(0xFFFFF0EF)
private val surfaceContainerLight = Color(0xFFFCEAE8)
private val surfaceContainerHighLight = Color(0xFFF6E4E2)
private val surfaceContainerHighestLight = Color(0xFFF1DEDD)

private val primaryDark = Color(0xFFFFB3AE)
private val onPrimaryDark = Color(0xFF571E1C)
private val primaryContainerDark = Color(0xFF733330)
private val onPrimaryContainerDark = Color(0xFFFFDAD7)
private val secondaryDark = Color(0xFFE7BDB9)
private val onSecondaryDark = Color(0xFF442927)
private val secondaryContainerDark = Color(0xFF5D3F3D)
private val onSecondaryContainerDark = Color(0xFFFFDAD7)
private val tertiaryDark = Color(0xFFE2C28C)
private val onTertiaryDark = Color(0xFF402D04)
private val tertiaryContainerDark = Color(0xFF594319)
private val onTertiaryContainerDark = Color(0xFFFFDEA6)
private val errorDark = Color(0xFFFFB4AB)
private val onErrorDark = Color(0xFF690005)
private val errorContainerDark = Color(0xFF93000A)
private val onErrorContainerDark = Color(0xFFFFDAD6)
private val backgroundDark = Color(0xFF1A1111)
private val onBackgroundDark = Color(0xFFF1DEDD)
private val surfaceDark = Color(0xFF1A1111)
private val onSurfaceDark = Color(0xFFF1DEDD)
private val surfaceVariantDark = Color(0xFF534342)
private val onSurfaceVariantDark = Color(0xFFD8C2BF)
private val outlineDark = Color(0xFFA08C8A)
private val outlineVariantDark = Color(0xFF534342)
private val scrimDark = Color(0xFF000000)
private val inverseSurfaceDark = Color(0xFFF1DEDD)
private val inverseOnSurfaceDark = Color(0xFF382E2D)
private val inversePrimaryDark = Color(0xFF904A46)
private val surfaceDimDark = Color(0xFF1A1111)
private val surfaceBrightDark = Color(0xFF423736)
private val surfaceContainerLowestDark = Color(0xFF140C0B)
private val surfaceContainerLowDark = Color(0xFF231919)
private val surfaceContainerDark = Color(0xFF271D1D)
private val surfaceContainerHighDark = Color(0xFF322827)
private val surfaceContainerHighestDark = Color(0xFF3D3231)

val LightRedColors = lightColorScheme(
    primary = primaryLight,
    onPrimary = onPrimaryLight,
    primaryContainer = primaryContainerLight,
    onPrimaryContainer = onPrimaryContainerLight,
    secondary = secondaryLight,
    onSecondary = onSecondaryLight,
    secondaryContainer = secondaryContainerLight,
    onSecondaryContainer = onSecondaryContainerLight,
    tertiary = tertiaryLight,
    onTertiary = onTertiaryLight,
    tertiaryContainer = tertiaryContainerLight,
    onTertiaryContainer = onTertiaryContainerLight,
    error = errorLight,
    onError = onErrorLight,
    errorContainer = errorContainerLight,
    onErrorContainer = onErrorContainerLight,
    background = backgroundLight,
    onBackground = onBackgroundLight,
    surface = surfaceLight,
    onSurface = onSurfaceLight,
    surfaceVariant = surfaceVariantLight,
    onSurfaceVariant = onSurfaceVariantLight,
    outline = outlineLight,
    inverseOnSurface = inverseOnSurfaceLight,
    inverseSurface = inverseSurfaceLight,
    inversePrimary = inversePrimaryLight,
    outlineVariant = outlineVariantLight,
    scrim = scrimLight,
    surfaceContainerLowest = surfaceContainerLowestLight,
    surfaceContainerLow = surfaceContainerLowLight,
    surfaceContainer = surfaceContainerLight,
    surfaceContainerHigh = surfaceContainerHighLight,
    surfaceContainerHighest = surfaceContainerHighestLight,
    surfaceBright = surfaceBrightLight,
    surfaceDim = surfaceDimLight,
)


val DarkRedColors = darkColorScheme(
    primary = primaryDark,
    onPrimary = onPrimaryDark,
    primaryContainer = primaryContainerDark,
    onPrimaryContainer = onPrimaryContainerDark,
    secondary = secondaryDark,
    onSecondary = onSecondaryDark,
    secondaryContainer = secondaryContainerDark,
    onSecondaryContainer = onSecondaryContainerDark,
    tertiary = tertiaryDark,
    onTertiary = onTertiaryDark,
    tertiaryContainer = tertiaryContainerDark,
    onTertiaryContainer = onTertiaryContainerDark,
    error = errorDark,
    onError = onErrorDark,
    errorContainer = errorContainerDark,
    onErrorContainer = onErrorContainerDark,
    background = backgroundDark,
    onBackground = onBackgroundDark,
    surface = surfaceDark,
    onSurface = onSurfaceDark,
    surfaceVariant = surfaceVariantDark,
    onSurfaceVariant = onSurfaceVariantDark,
    outline = outlineDark,
    inverseOnSurface = inverseOnSurfaceDark,
    inverseSurface = inverseSurfaceDark,
    inversePrimary = inversePrimaryDark,
    outlineVariant = outlineVariantDark,
    scrim = scrimDark,
    surfaceContainerLowest = surfaceContainerLowestDark,
    surfaceContainerLow = surfaceContainerLowDark,
    surfaceContainer = surfaceContainerDark,
    surfaceContainerHigh = surfaceContainerHighDark,
    surfaceContainerHighest = surfaceContainerHighestDark,
    surfaceBright = surfaceBrightDark,
    surfaceDim = surfaceDimDark,
)