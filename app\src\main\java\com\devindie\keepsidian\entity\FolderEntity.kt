package com.devindie.keepsidian.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.yangdai.opennote.presentation.theme.Blue
import com.yangdai.opennote.presentation.theme.Cyan
import com.yangdai.opennote.presentation.theme.Green
import com.yangdai.opennote.presentation.theme.Orange
import com.yangdai.opennote.presentation.theme.Purple
import com.yangdai.opennote.presentation.theme.Red
import com.yangdai.opennote.presentation.theme.Yellow
import kotlinx.serialization.Serializable

@Serializable
@Entity
data class FolderEntity(
    @PrimaryKey val id: Long? = null,
    val name: String = "",
    val color: Int? = null
) {
    companion object {
        val folderColors = listOf(
            Red,
            Orange,
            Yellow,
            Green,
            <PERSON>an,
            <PERSON>,
            Purple
        )
    }
}