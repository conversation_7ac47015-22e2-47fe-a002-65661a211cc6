<resources>
    <!-- App Info -->
    <string name="app_name">Keepsidian</string>

    <!-- Widget Descriptions -->
    <string name="daily_todo_widget_description">Display today\'s tasks from your Vault</string>
    <string name="quick_actions_widget_description">Quick access to Speech-to-Text and OCR Image features</string>
    <string name="view_all_notes">View all notes</string>
    <string name="add_calendar_event">Add Task</string>

    <!-- Main Activity -->
    <string name="ocr_auto_enabled_toast">OCR was disabled. Enabling it for you now.</string>
    <string name="speech_to_text_auto_enabled_toast">Speech-To-Text was disabled. Enabling it for you now.</string>
    <string name="vault_config_required">Please select a vault to continue</string>
    <string name="features">Features</string>
    <string name="features_vault_required">Features (Vault selection required)</string>
    <string name="enabled">Enabled</string>
    <string name="disabled">Disabled</string>
    <string name="select_vault_to_enable">Select a vault to enable this feature</string>
    <string name="ocr_image_processing">OCR Image Processing</string>
    <string name="ocr_description">Enable OCR (Optical Character Recognition) for processing images shared to the app.\nThis feature requires storage permissions (ALLOW ALL) to access shared images.</string>
    <string name="ocr_enabled">OCR Enabled</string>
    <string name="ocr_currently_disabled">OCR is currently disabled. Enable this feature to process text from images.</string>
    <string name="speech_to_text">Speech-To-Text</string>
    <string name="speech_to_text_description">Enable Voice Note for recording voice notes. Maximum recording time is %1$d seconds.\nThis feature requires microphone permission to record audio.</string>
    <string name="offline_speech_recognition">Offline Speech-To-Text (STT) (English)</string>
    <string name="offline_speech_recognition_description">Enable offline speech recognition using Vosk. This will download a small model (about 50MB) for processing speech without an internet connection.</string>
    <string name="offline_speech_recognition_enabled">Offline STT Enabled</string>
    <string name="offline_speech_recognition_disabled">Offline STT Disabled</string>
    <string name="use_offline_recognition">Use Offline Recognition</string>
    <string name="downloading_speech_model">Downloading STT model…\n(please do not close app until it\'s done)</string>
    <string name="speech_model_download_failed">Failed to download speech recognition model</string>
    <string name="offline_transcription_complete">Offline transcription complete</string>
    <string name="auto_youtube_transcript">Get YouTube Transcript Automatically</string>
    <string name="auto_youtube_transcript_description">When enabled, the app will automatically extract video metadata and subtitles when you share a YouTube link.</string>

    <!-- Vault Selector -->
    <string name="select_vault">Select Vault</string>
    <string name="vault_configuration">Vault Configuration</string>
    <string name="vault_not_configured">Not configured</string>
    <string name="vault_configured">Configured</string>
    <string name="vault_path">Vault Path: %1$s</string>
    <string name="attachment_folder">Attachment Folder: %1$s</string>
    <string name="daily_notes_folder">Daily Notes Folder: %1$s</string>
    <string name="save_location">Save Location: %1$s</string>
    <string name="refresh_configuration">Refresh</string>
    <string name="refresh_warning">Update configuration if you make any changes in Obsidian\'s settings</string>
    <string name="invalid_vault_title">Invalid Vault Selection</string>
    <string name="invalid_vault_message">The selected folder is not a valid vault. Please select a folder that contains an .obsidian folder.</string>

    <!-- Speech Recording Activity -->
    <string name="speech_to_text_disabled">Speech-To-Text is disabled. Please enable it in settings.</string>
    <string name="vault_not_configured_message">Vault not configured</string>
    <string name="no_speech_detected">No speech detected and no audio recorded</string>
    <string name="no_speech_placeholder">[No speech detected]</string>
    <string name="failed_to_start_audio_recording">Failed to start audio recording</string>
    <string name="speech_recognition_failed">Speech recognition failed to start</string>
    <string name="voice_recording">Voice Recording</string>
    <string name="speak_now">Speak now…</string>
    <string name="save">Save</string>
    <string name="cancel">Cancel</string>
    <string name="seconds_remaining">%1$ds</string>
    <string name="start_recording">Start Recording</string>
    <string name="stop_recording">Stop Recording</string>
    <string name="max_recording_time_info">Maximum recording time: %1$d seconds</string>
    <string name="voice_note">Voice Note</string>
    <string name="audio_recording">Audio Recording</string>
    <string name="transcription">Transcription</string>

    <!-- Share Intent Activity -->
    <string name="unsupported_content_type">Unsupported content type: %1$s</string>
    <string name="unsupported_action">Unsupported action</string>
    <string name="no_text_shared">No text was shared</string>
    <string name="ocr_disabled">OCR image processing is disabled. Please enable it in the app settings.</string>
    <string name="vault_not_configured_open_app">Vault not configured. Please open the app and select a vault first.</string>
    <string name="cannot_access_image">Cannot access the shared image. Please try sharing it again.</string>

    <!-- Speech Recognition Errors -->
    <string name="error_audio_recording">Audio recording error</string>
    <string name="error_client">Client side error</string>
    <string name="error_insufficient_permissions">Insufficient permissions</string>
    <string name="error_network">Network error</string>
    <string name="error_network_timeout">Network timeout</string>
    <string name="error_no_match">No match found</string>
    <string name="error_recognizer_busy">RecognitionService busy</string>
    <string name="error_server">Server error</string>
    <string name="error_speech_timeout">No speech input</string>
    <string name="error_unknown">Unknown error</string>

    <!-- Notification Permission -->
    <string name="notification_permission_title">Notification Permission</string>
    <string name="notification_permission_message">Notifications are used to inform you when a note has been saved to your vault. Without this permission, you won\'t be notified when the process is complete.</string>
    <string name="settings">Settings</string>
    <string name="permission_denied">Permission denied</string>

    <!-- Calendar Event Activity -->
    <string name="please_enter_event_description">Please enter task description</string>
    <string name="date_time_format">EEE, MMM d, yyyy \'at\' h:mm a</string>
    <string name="task_mark_as_done">Mark as done in today\'s note</string>
    <string name="task_mark_as_undone">Mark as undone in today\'s note</string>
    <string name="create_task">Create Task</string>
    <string name="task_description">Task Description</string>
    <string name="task_date_time">Task Date and Time</string>
    <string name="enter_task_description">Enter task description</string>

    <!-- Daily Todo Widget -->
    <string name="no_tasks_for_today">No tasks for today</string>
    <string name="loading_tasks">Loading tasks…</string>
    <string name="voice_recording_content_description">Voice Recording</string>
    <string name="camera_content_description">Camera</string>
    <string name="date_format">MM/dd</string>

    <!-- Notifications -->
    <string name="notification_channel_name">keepsidian</string>
    <string name="voice_note_saved">Voice Note Saved</string>
    <string name="audio_transcription_saved">Audio and transcription have been saved to your vault</string>
    <string name="processing_notification">Processing your content…</string>
    <string name="processing_speech">Processing speech…</string>

    <!-- Toast Messages -->
    <string name="processing_youtube_link">Processing YouTube link…</string>
    <string name="processing_image_ocr">Processing image with OCR…</string>
    <string name="saving_image">Saving image to Obsidian vault…</string>
    <string name="no_image_shared">No image was shared</string>
    <string name="storage_permission_required">Storage permission is required to access images. Please grant permission and try again.</string>
    <string name="failed_to_capture_image">Failed to capture image or operation cancelled</string>
    <string name="failed_to_launch_camera">Failed to launch camera</string>
    <string name="failed_to_create_image_file">Failed to create image file: %1$s</string>

    <!-- Worker Keys -->
    <string name="key_is_already_formatted">IS_ALREADY_FORMATTED</string>

    <!-- Markdown Formatting -->
    <string name="tags_voice_transcription">[voice, transcription]</string>
    <string name="tags_ocr_image">[ocr, image]</string>
    <string name="markdown_title">title</string>
    <string name="markdown_date">date</string>
    <string name="markdown_tags">tags</string>
    <string name="ocr_image">OCR Image</string>
    <string name="extracted_text">Extracted Text</string>
    <string name="no_text_detected">No text was detected in this image.</string>
    <string name="ocr_note">*Note: This text was extracted using OCR. Some inaccuracies may be present.*</string>
    <string name="image_dimensions">*Image dimensions: %1$dx%2$d*</string>

    <!-- Notifications -->
    <string name="processing_image">Processing Image</string>
    <string name="ocr_in_progress">OCR in progress…</string>
    <string name="shows_progress_ocr">Shows progress of OCR processing</string>

    <!-- Today's Note Links -->
    <string name="adding_link_to_todays_note">Adding link to today\'s note…</string>
    <string name="adding_link_to_notes">Adding links to today\'s note and future date\'s note…</string>
    <string name="link_added_to_todays_note">Link added to today\'s note</string>

    <!-- VaultSelector -->
    <string name="reading_configuration">Reading configuration…</string>
    <string name="change_obsidian_vault">Change Vault</string>
    <string name="obsidian_icon_description">Obsidian Icon</string>
    <string name="must_contain_obsidian_folder">(must contain .obsidian folder)</string>
    <string name="refresh_settings_info">If you change settings in Obsidian, click Refresh to update the configuration.</string>
    <string name="valid_vault_note">Note: A valid vault must contain (hidden) .obsidian folder.</string>
    <string name="vault_no_longer_valid">The vault no longer contains an .obsidian folder. Please select a valid vault.</string>

    <!-- SpeechToTextToggle -->

    <!-- DailyToDoWidget -->
    <string name="voice_recording_button">Voice Recording</string>
    <string name="camera_button">Camera</string>
    <string name="tasks">Tasks</string>
    <string name="callouts">Callouts</string>

    <!-- OCRImageToggle -->
    <string name="ocr_title">OCR Image Processing</string>
</resources>
