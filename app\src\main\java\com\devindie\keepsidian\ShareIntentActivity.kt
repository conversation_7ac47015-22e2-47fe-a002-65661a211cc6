package com.devindie.keepsidian

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import com.devindie.keepsidian.services.OCRImageService
import com.devindie.keepsidian.services.SaveTextService
import com.devindie.keepsidian.services.YouTubeMetadataService
import java.util.regex.Pattern

class ShareIntentActivity : ComponentActivity() {

    companion object {
        private const val TAG = "ShareIntentActivity"

        // YouTube video ID regex pattern
        private val YOUTUBE_PATTERN = Pattern.compile(
            "(?:https?:\\/\\/)?(?:www\\.)?(?:youtube\\.com\\/(?:watch\\?v=|embed\\/|shorts\\/)|youtu\\.be\\/)([a-zA-Z0-9_-]{11})"
        )

        // Keys for WorkManager data
        const val KEY_VIDEO_ID = "video_id"
        const val KEY_SHARED_TEXT = "shared_text"
        const val KEY_VAULT_URI = "vault_uri"
        const val KEY_ATTACHMENT_FOLDER = "attachment_folder"
        const val KEY_IMAGE_URI = "image_uri"
    }

    private lateinit var notificationPermissionLauncher: ActivityResultLauncher<String>
    private lateinit var storagePermissionLauncher: ActivityResultLauncher<Array<String>>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Register notification permission launcher
        notificationPermissionLauncher = NotificationPermissionHelper.registerPermissionLauncher(this)

        // Register storage permission launcher
        storagePermissionLauncher = StoragePermissionHelper.registerPermissionLauncher(this)

        // Request notification permission if needed
        NotificationPermissionHelper.requestNotificationPermissionIfNeeded(
            this,
            notificationPermissionLauncher
        )

        // Request storage permission if needed
        StoragePermissionHelper.requestStoragePermissionIfNeeded(
            this,
            storagePermissionLauncher
        )

        // Handle the intent
        when (intent?.action) {
            Intent.ACTION_SEND -> {
                when {
                    intent.type?.startsWith(Constants.MIME_TYPE_TEXT) == true -> {
                        handleSharedText(intent)
                    }
                    intent.type?.startsWith(Constants.MIME_TYPE_IMAGE) == true -> {
                        handleSharedImage(intent)
                    }
                    else -> {
                        showError(getString(R.string.unsupported_content_type, intent.type ?: ""))
                    }
                }
            }
            else -> {
                showError(getString(R.string.unsupported_action))
            }
        }

        // Always finish the activity after processing
        finish()
    }

    private fun handleSharedText(intent: Intent) {
        val sharedText = intent.getStringExtra(Intent.EXTRA_TEXT)
        if (sharedText.isNullOrEmpty()) {
            showError(getString(R.string.no_text_shared))
            return
        }

        Log.d(TAG, "Received shared text: $sharedText")

        // Check if the Obsidian vault is configured
        val appDataStore = AppModule.provideAppDataStoreRepository(applicationContext)
        val vaultUriString = appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_VAULT_URI, "")

        if (vaultUriString.isEmpty()) {
            showError(getString(R.string.vault_not_configured_open_app))
            return
        }

        // Check if it's a YouTube link
        val matcher = YOUTUBE_PATTERN.matcher(sharedText)
        if (matcher.find()) {
            val videoId = matcher.group(1)
            Log.d(TAG, "Found YouTube video ID: $videoId")

            // Check if auto YouTube transcript is enabled
            // This preference can be used to determine if we should try to get the transcript
            // For now, we're just getting metadata regardless of this setting
            appDataStore.getBooleanValue(
                Constants.Preferences.AUTO_YOUTUBE_TRANSCRIPT,
                true
            )

            if (videoId != null) {
                handleYouTubeLink(videoId, sharedText, vaultUriString)
            } else {
                // Just save the text as a regular note
                saveTextToObsidian(sharedText, vaultUriString)
            }
        } else {
            // Not a YouTube link, just save the text
            saveTextToObsidian(sharedText, vaultUriString)
        }
    }

    private fun handleYouTubeLink(videoId: String, sharedText: String, vaultUriString: String) {
        // Start the YouTube metadata service
        YouTubeMetadataService.startService(
            this,
            videoId,
            sharedText,
            vaultUriString
        )
        
        Toast.makeText(
            this,
            getString(R.string.processing_youtube_link),
            Toast.LENGTH_SHORT
        ).show()
        
        finish()
    }

    private fun saveTextToObsidian(text: String, vaultUriString: String) {
        // Start the save text service
        SaveTextService.startService(
            this,
            text,
            vaultUriString
        )
        
        Toast.makeText(
            this,
            "Saving to Obsidian...",
            Toast.LENGTH_SHORT
        ).show()
        
        finish()
    }

    private fun handleSharedImage(intent: Intent) {
        val imageUri = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            intent.getParcelableExtra(Intent.EXTRA_STREAM, Uri::class.java)
        } else {
            @Suppress("DEPRECATION")
            intent.getParcelableExtra(Intent.EXTRA_STREAM)
        }

        if (imageUri == null) {
            showError(getString(R.string.no_image_shared))
            return
        }

        Log.d(TAG, "Received shared image: $imageUri")

        // Check if we have storage permissions
        if (!StoragePermissionHelper.hasStoragePermission(this)) {
            Log.d(TAG, "Storage permission not granted, requesting...")
            StoragePermissionHelper.requestStoragePermissionIfNeeded(this, storagePermissionLauncher)
            showError(getString(R.string.storage_permission_required))
            return
        }

        // Take persistent URI permission to access the shared content
        try {
            // Request read permission for the URI
            val takeFlags = Intent.FLAG_GRANT_READ_URI_PERMISSION
            contentResolver.takePersistableUriPermission(imageUri, takeFlags)
            Log.d(TAG, "Successfully took persistable URI permission for: $imageUri")
        } catch (e: SecurityException) {
            Log.e(TAG, "Failed to take persistable URI permission: ${e.message}")
            // Continue anyway as some URIs might not support persistable permissions
        }

        // Check if the Obsidian vault is configured
        val appDataStore = AppModule.provideAppDataStoreRepository(applicationContext)
        val vaultUriString = appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_VAULT_URI, "")
        val attachmentFolderPath = appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_ATTACHMENT_FOLDER, "")
        appDataStore.getStringValue(Constants.Preferences.OBSIDIAN_SAVE_LOCATION, "Clippings")

        // Check if OCR is enabled
        val isOCREnabled = appDataStore.getBooleanValue(Constants.Preferences.ENABLE_OCR_IMAGES, false)

        if (!isOCREnabled) {
            showError(getString(R.string.ocr_disabled))
            return
        }

        if (vaultUriString.isEmpty()) {
            showError(getString(R.string.vault_not_configured_open_app))
            return
        }

        // Check if we can access the URI
        if (!canAccessUri(imageUri)) {
            showError(getString(R.string.cannot_access_image))
            return
        }

        processImageWithOCR(imageUri, vaultUriString, attachmentFolderPath)
    }

    private fun processImageWithOCR(imageUri: Uri, vaultUriString: String, attachmentFolderPath: String) {
        // Start the OCR image service
        OCRImageService.startService(
            this,
            imageUri.toString(),
            vaultUriString,
            attachmentFolderPath
        )
        
        Toast.makeText(
            this,
            getString(R.string.processing_image_ocr),
            Toast.LENGTH_SHORT
        ).show()
        
        finish()
    }

    private fun showError(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
        Log.e(TAG, message)
    }

    /**
     * Check if we can access the URI
     */
    private fun canAccessUri(uri: Uri): Boolean {
        return try {
            // Try to get the MIME type
            val mimeType = contentResolver.getType(uri)
            if (mimeType == null) {
                Log.e(TAG, "Cannot determine MIME type for URI: $uri")
                return false
            }

            // Try to open the input stream
            contentResolver.openInputStream(uri)?.use {
                // Just check if we can open it, no need to read anything
                true
            } == true
        } catch (e: SecurityException) {
            Log.e(TAG, "Security exception when accessing URI: $uri", e)
            false
        } catch (e: Exception) {
            Log.e(TAG, "Error accessing URI: $uri", e)
            false
        }
    }
}
